# -*- coding: utf-8 -*-
"""
消失点检测模块
"""
import numpy as np
from .geometry_utils import lines_normal, line_hmg_intersect


def ransac_vanishing_point(lines_homo, params):
    """
    使用RANSAC算法检测消失点
    
    Args:
        lines_homo: 齐次线坐标 (3, N)
        params: 参数对象
    
    Returns:
        best_vp: 最佳消失点
        best_inliers: 最佳内点索引
    """
    if lines_homo.shape[1] < 2:
        return np.array([0, 0, 1]), []
    
    max_inliers = 0
    best_vp = np.array([0, 0, 1])
    best_inliers = []
    
    n_lines = lines_homo.shape[1]
    
    for _ in range(params.ransac_iter_num):
        # 随机选择两条线
        if n_lines < 2:
            break
        
        indices = np.random.choice(n_lines, 2, replace=False)
        line1 = lines_homo[:, indices[0]]
        line2 = lines_homo[:, indices[1]]
        
        # 计算交点（消失点）
        vp = np.cross(line1, line2)
        if abs(vp[2]) > 1e-10:
            vp = vp / vp[2]
        
        # 计算内点
        inliers = []
        for i in range(n_lines):
            line = lines_homo[:, i]
            # 计算点到线的距离
            dist = abs(np.dot(vp, line))
            
            if dist < params.ransac_inlier_ratio:
                inliers.append(i)
        
        # 更新最佳结果
        if len(inliers) > max_inliers:
            max_inliers = len(inliers)
            best_inliers = inliers
            best_vp = vp
    
    # 使用所有内点重新计算消失点
    if len(best_inliers) >= 2:
        inlier_lines = lines_homo[:, best_inliers]
        best_vp = lines_normal(inlier_lines)
    
    return best_vp, best_inliers


def detect_zenith_point(lines_homo, zenith_line, params):
    """
    检测天顶点
    
    Args:
        lines_homo: 齐次线坐标
        zenith_line: 天顶线
        params: 参数对象
    
    Returns:
        zenith_point: 天顶点
        zenith_group: 天顶线组
    """
    if lines_homo.shape[1] == 0:
        return np.array([0, 1, 0]), []
    
    # 计算线段与天顶线的角度
    zenith_angle = np.arctan2(-zenith_line[1], -zenith_line[0]) * 180 / np.pi
    
    # 找到接近垂直的线段
    vertical_indices = []
    for i in range(lines_homo.shape[1]):
        line = lines_homo[:, i]
        line_angle = np.arctan2(-line[1], -line[0]) * 180 / np.pi
        angle_diff = abs(line_angle - zenith_angle)
        angle_diff = min(angle_diff, 180 - angle_diff)
        
        if angle_diff < params.theta_z:
            vertical_indices.append(i)
    
    # 计算天顶点
    if len(vertical_indices) >= 2:
        vertical_lines = lines_homo[:, vertical_indices]
        zenith_point = lines_normal(vertical_lines)
    else:
        zenith_point = np.array([0, 1, 0])
    
    return zenith_point, vertical_indices


def detect_horizontal_vanishing_points(lines_homo, zenith_point, params):
    """
    检测水平消失点
    
    Args:
        lines_homo: 齐次线坐标
        zenith_point: 天顶点
        params: 参数对象
    
    Returns:
        hvps: 水平消失点列表
        hvp_groups: 水平消失点对应的线段组
    """
    if lines_homo.shape[1] == 0:
        return [], []
    
    # 过滤掉垂直线段
    horizontal_indices = []
    for i in range(lines_homo.shape[1]):
        line = lines_homo[:, i]
        # 计算线段与天顶点的角度
        cos_angle = abs(np.dot(zenith_point, line))
        angle = np.arcsin(np.clip(cos_angle, 0, 1)) * 180 / np.pi
        
        if angle > params.theta_h:  # 不是垂直线
            horizontal_indices.append(i)
    
    if len(horizontal_indices) < 2:
        return [], []
    
    horizontal_lines = lines_homo[:, horizontal_indices]
    
    # 使用RANSAC检测多个水平消失点
    hvps = []
    hvp_groups = []
    remaining_indices = list(range(horizontal_lines.shape[1]))
    
    while len(remaining_indices) >= 2:
        # 从剩余线段中检测消失点
        remaining_lines = horizontal_lines[:, remaining_indices]
        vp, inliers_local = ransac_vanishing_point(remaining_lines, params)
        
        if len(inliers_local) < 2:
            break
        
        # 转换回全局索引
        inliers_global = [horizontal_indices[remaining_indices[i]] for i in inliers_local]
        
        hvps.append(vp)
        hvp_groups.append(inliers_global)
        
        # 移除已使用的线段
        remaining_indices = [idx for i, idx in enumerate(remaining_indices) if i not in inliers_local]
    
    return hvps, hvp_groups


def refine_vanishing_point(lines_homo, initial_vp, params):
    """
    精化消失点
    
    Args:
        lines_homo: 齐次线坐标
        initial_vp: 初始消失点
        params: 参数对象
    
    Returns:
        refined_vp: 精化后的消失点
    """
    if lines_homo.shape[1] < 2:
        return initial_vp
    
    vp = initial_vp.copy()
    
    for _ in range(params.refine_niters):
        # 找到支持当前消失点的线段
        inliers = []
        for i in range(lines_homo.shape[1]):
            line = lines_homo[:, i]
            dist = abs(np.dot(vp, line))
            if dist < params.theta_con * np.pi / 180:
                inliers.append(i)
        
        if len(inliers) >= 2:
            inlier_lines = lines_homo[:, inliers]
            vp = lines_normal(inlier_lines)
        else:
            break
    
    return vp
