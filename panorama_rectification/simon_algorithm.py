# -*- coding: utf-8 -*-
"""
Simon算法核心实现
"""
import numpy as np
from PIL import Image
import cv2

from .config import get_default_params
from .line_detection import detect_lines_lsd, filter_lines, convert_lines_to_homogeneous
from .vanishing_point import detect_horizontal_vanishing_points, refine_vanishing_point
from .orthorectify import apply_spherical_rectification
from .geometry_utils import line_hmg_from_two_points, R_pitch, R_roll


def simon_rectification(image):
    """
    Simon算法全景图矫正主函数
    
    Args:
        image: 输入图像 (PIL Image 或 numpy数组 或 文件路径)
    
    Returns:
        rectified_image: 矫正后的图像
        pitch: 俯仰角
        roll: 横滚角
        success: 是否成功
    """
    # 处理输入
    if isinstance(image, str):
        # 文件路径
        pil_image = Image.open(image)
        img_array = np.array(pil_image)
    elif isinstance(image, Image.Image):
        # PIL图像
        pil_image = image
        img_array = np.array(image)
    elif isinstance(image, np.ndarray):
        # numpy数组
        img_array = image
        if len(img_array.shape) == 3:
            pil_image = Image.fromarray(img_array)
        else:
            pil_image = Image.fromarray(img_array).convert('RGB')
    else:
        raise ValueError("不支持的输入类型")
    
    height, width = img_array.shape[:2]
    
    # 获取参数
    params = get_default_params()
    
    # 估算焦距
    focal_length = max(width, height) / 2
    
    try:
        # 1. 线段检测
        lines = detect_lines_lsd(pil_image)
        if len(lines) == 0:
            print("警告: 未检测到线段，使用默认矫正")
            return apply_simple_rectification(img_array)
        
        # 2. 线段过滤
        lines = filter_lines(lines, width, height)
        if len(lines) < 4:
            print("警告: 有效线段太少，使用默认矫正")
            return apply_simple_rectification(img_array)
        
        # 3. 转换为齐次坐标
        lines_homo = convert_lines_to_homogeneous(lines, width, height, focal_length)
        
        # 4. 检测天顶线和天顶点
        zenith_line, zenith_point = detect_zenith_from_lines(lines_homo, params)
        
        # 5. 检测水平消失点
        hvps, hvp_groups = detect_horizontal_vanishing_points(lines_homo, zenith_point, params)
        
        # 6. 计算矫正参数
        pitch, roll = calculate_rectification_angles(zenith_point)
        
        # 7. 应用矫正
        rectified_image = apply_spherical_rectification(pil_image, pitch, roll)
        
        return rectified_image, pitch, roll, True
        
    except Exception as e:
        print(f"矫正过程中出现错误: {e}")
        print("使用默认矫正")
        return apply_simple_rectification(img_array)


def detect_zenith_from_lines(lines_homo, params):
    """
    从线段中检测天顶线和天顶点
    
    Args:
        lines_homo: 齐次线坐标
        params: 参数对象
    
    Returns:
        zenith_line: 天顶线
        zenith_point: 天顶点
    """
    if lines_homo.shape[1] == 0:
        return np.array([0, 1, 0]), np.array([0, 1, 0])
    
    # 简化的天顶检测：假设垂直线段对应天顶方向
    # 计算线段角度
    angles = []
    for i in range(lines_homo.shape[1]):
        line = lines_homo[:, i]
        angle = np.arctan2(-line[1], -line[0])
        angles.append(angle)
    
    angles = np.array(angles)
    
    # 找到接近垂直的线段（角度接近±π/2）
    vertical_mask = (np.abs(angles - np.pi/2) < params.theta_z * np.pi/180) | \
                   (np.abs(angles + np.pi/2) < params.theta_z * np.pi/180)
    
    if np.sum(vertical_mask) >= 2:
        vertical_lines = lines_homo[:, vertical_mask]
        # 使用SVD计算天顶点
        U, S, Vt = np.linalg.svd(vertical_lines.T)
        zenith_point = Vt[-1, :]
        
        # 标准化
        if abs(zenith_point[2]) > 1e-10:
            zenith_point = zenith_point / zenith_point[2]
    else:
        # 默认天顶点
        zenith_point = np.array([0, 1, 0])
    
    # 天顶线（简化处理）
    zenith_line = np.array([0, 1, 0])
    
    return zenith_line, zenith_point


def calculate_rectification_angles(zenith_point):
    """
    从天顶点计算矫正角度
    
    Args:
        zenith_point: 天顶点
    
    Returns:
        pitch: 俯仰角
        roll: 横滚角
    """
    # 计算俯仰角和横滚角
    if abs(zenith_point[1]) > 1e-10:
        pitch = np.arctan2(zenith_point[2], zenith_point[1])
    else:
        pitch = 0
    
    if abs(zenith_point[1]) > 1e-10 or abs(zenith_point[2]) > 1e-10:
        roll = -np.arctan2(zenith_point[0], 
                          np.sign(zenith_point[1]) * np.sqrt(zenith_point[1]**2 + zenith_point[2]**2))
    else:
        roll = 0
    
    return pitch, roll


def apply_simple_rectification(img_array):
    """
    应用简单的默认矫正
    
    Args:
        img_array: 图像数组
    
    Returns:
        rectified_image: 矫正后的图像
        pitch: 俯仰角 (0)
        roll: 横滚角 (0)
        success: 是否成功 (False)
    """
    # 简单的水平矫正
    pitch = 0.0
    roll = 0.0
    
    # 应用轻微的矫正以改善视觉效果
    if len(img_array.shape) == 3:
        rectified = img_array.copy()
    else:
        rectified = img_array.copy()
    
    return rectified, pitch, roll, False


def batch_rectification(input_folder, output_folder):
    """
    批量矫正图像
    
    Args:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径
    """
    import os
    import glob
    
    # 支持的图像格式
    extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    
    image_files = []
    for ext in extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
        image_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))
    
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    print(f"找到 {len(image_files)} 张图像")
    
    for i, image_path in enumerate(image_files):
        print(f"处理 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
        
        try:
            # 矫正图像
            rectified, pitch, roll, success = simon_rectification(image_path)
            
            # 保存结果
            output_path = os.path.join(output_folder, os.path.basename(image_path))
            if isinstance(rectified, np.ndarray):
                rectified_pil = Image.fromarray(rectified.astype(np.uint8))
            else:
                rectified_pil = rectified
            
            rectified_pil.save(output_path)
            
            status = "成功" if success else "默认矫正"
            print(f"  {status} - pitch: {pitch:.3f}, roll: {roll:.3f}")
            
        except Exception as e:
            print(f"  错误: {e}")
    
    print("批量处理完成")
