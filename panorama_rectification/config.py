# -*- coding: utf-8 -*-
"""
全景图矫正参数配置
"""
import numpy as np


class RectificationParams:
    """全景图矫正参数类"""
    
    # Simon算法核心参数
    theta_v = np.pi / 8      # 垂直角度阈值
    theta_z = 10             # 天顶角度阈值
    L_z = 45                 # 天顶线长度阈值
    theta_h = 1.5            # 水平角度阈值
    L_h = 64                 # 水平线长度阈值
    sigma = 0.2              # 高斯核标准差
    S = 300                  # 采样点数
    L_vp = 128               # 消失点线长度阈值
    
    # 消失点检测参数
    include_infinite_hvps = 1  # 是否包含无限远水平消失点
    theta_con = 1.5           # 一致性角度阈值
    theta_verline = 15        # 垂直线角度阈值
    theta_horline = 1         # 水平线角度阈值
    hvp_refinement = True     # 是否进行消失点精化
    refine_niters = 3         # 精化迭代次数
    
    # RANSAC参数
    ransac_iter_num = 50      # RANSAC迭代次数
    ransac_inlier_ratio = 0.02  # 内点比例阈值
    
    def score_function(self, x):
        """评分函数"""
        return (self.theta_con - x) * (self.theta_con > x)


def get_default_params():
    """获取默认参数"""
    return RectificationParams()
