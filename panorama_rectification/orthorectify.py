# -*- coding: utf-8 -*-
"""
正交矫正模块
"""
import numpy as np
import cv2
from PIL import Image
import skimage.transform
from scipy.ndimage import map_coordinates


def calculate_rectification_transform(zenith_point, horizon_line, hvps):
    """
    计算矫正变换矩阵
    
    Args:
        zenith_point: 天顶点
        horizon_line: 地平线
        hvps: 水平消失点列表
    
    Returns:
        transform_matrix: 变换矩阵
    """
    # 计算俯仰角和横滚角
    pitch = np.arctan2(zenith_point[2], zenith_point[1])
    roll = -np.arctan2(zenith_point[0], 
                       np.sign(zenith_point[1]) * np.sqrt(zenith_point[1]**2 + zenith_point[2]**2))
    
    # 构建旋转矩阵
    R_pitch = np.array([[1, 0, 0],
                        [0, np.cos(pitch), -np.sin(pitch)],
                        [0, np.sin(pitch), np.cos(pitch)]])
    
    R_roll = np.array([[np.cos(roll), 0, np.sin(roll)],
                       [0, 1, 0],
                       [-np.sin(roll), 0, np.cos(roll)]])
    
    R = R_roll @ R_pitch
    
    return R, pitch, roll


def apply_spherical_rectification(image, pitch, roll):
    """
    应用球面矫正
    
    Args:
        image: 输入图像 (PIL Image)
        pitch: 俯仰角
        roll: 横滚角
    
    Returns:
        rectified_image: 矫正后的图像
    """
    if isinstance(image, Image.Image):
        img_array = np.array(image)
    else:
        img_array = image
    
    height, width = img_array.shape[:2]
    
    # 创建球面坐标网格
    u, v = np.mgrid[0:height, 0:width]
    
    # 转换为球面坐标
    theta = (v - width/2) * 2 * np.pi / width  # 经度
    phi = (u - height/2) * np.pi / height      # 纬度
    
    # 球面坐标转笛卡尔坐标
    x = np.cos(phi) * np.sin(theta)
    y = np.sin(phi)
    z = np.cos(phi) * np.cos(theta)
    
    # 应用旋转
    coords = np.stack([x, y, z], axis=-1)
    coords_flat = coords.reshape(-1, 3)
    
    # 旋转矩阵
    R_pitch = np.array([[1, 0, 0],
                        [0, np.cos(pitch), -np.sin(pitch)],
                        [0, np.sin(pitch), np.cos(pitch)]])
    
    R_roll = np.array([[np.cos(roll), 0, np.sin(roll)],
                       [0, 1, 0],
                       [-np.sin(roll), 0, np.cos(roll)]])
    
    R = R_roll @ R_pitch
    coords_rotated = coords_flat @ R.T
    coords_rotated = coords_rotated.reshape(height, width, 3)
    
    # 转换回球面坐标
    x_rot, y_rot, z_rot = coords_rotated[:,:,0], coords_rotated[:,:,1], coords_rotated[:,:,2]
    
    phi_new = np.arcsin(np.clip(y_rot, -1, 1))
    theta_new = np.arctan2(x_rot, z_rot)
    
    # 转换为图像坐标
    u_new = phi_new * height / np.pi + height/2
    v_new = theta_new * width / (2*np.pi) + width/2
    
    # 处理边界
    u_new = np.clip(u_new, 0, height-1)
    v_new = np.clip(v_new, 0, width-1)
    
    # 插值
    if len(img_array.shape) == 3:
        rectified = np.zeros_like(img_array)
        for c in range(img_array.shape[2]):
            rectified[:,:,c] = map_coordinates(img_array[:,:,c], [u_new, v_new], 
                                             order=1, mode='wrap')
    else:
        rectified = map_coordinates(img_array, [u_new, v_new], order=1, mode='wrap')
    
    return rectified


def orthorectify_facade(image, zenith_point, horizon_line, hvp, focal_length):
    """
    对立面进行正交矫正
    
    Args:
        image: 输入图像
        zenith_point: 天顶点
        horizon_line: 地平线
        hvp: 水平消失点
        focal_length: 焦距
    
    Returns:
        rectified_image: 矫正后的图像
        transform_matrix: 变换矩阵
    """
    if isinstance(image, Image.Image):
        img_array = np.array(image)
        width, height = image.size
    else:
        img_array = image
        height, width = img_array.shape[:2]
    
    # 构建相机内参矩阵
    K = np.array([[focal_length, 0, width/2],
                  [0, focal_length, height/2],
                  [0, 0, 1]])
    
    # 计算消失点在图像坐标系中的位置
    if abs(hvp[2]) > 1e-10:
        hvp_img = hvp / hvp[2]
        hvp_img = K @ hvp_img
    else:
        # 无限远点的处理
        hvp_img = np.array([width/2, height/2, 1])
    
    zenith_img = zenith_point
    if abs(zenith_img[2]) > 1e-10:
        zenith_img = zenith_img / zenith_img[2]
        zenith_img = K @ zenith_img
    
    # 定义矫正后的四个角点
    corners_rect = np.array([[0, 0, 1],
                            [width, 0, 1],
                            [width, height, 1],
                            [0, height, 1]]).T
    
    # 定义原始图像中对应的四个角点
    # 这里使用简化的方法，实际应用中可能需要更复杂的计算
    corners_orig = np.array([[0, 0, 1],
                            [width, 0, 1],
                            [width, height, 1],
                            [0, height, 1]]).T
    
    # 计算单应性矩阵
    try:
        H = cv2.findHomography(corners_orig[:2].T, corners_rect[:2].T)[0]
        if H is None:
            H = np.eye(3)
    except:
        H = np.eye(3)
    
    # 应用变换
    rectified = cv2.warpPerspective(img_array, H, (width, height))
    
    return rectified, H


def simple_rectification(image, pitch, roll, heading=0):
    """
    简单的全景图矫正
    
    Args:
        image: 输入图像
        pitch: 俯仰角
        roll: 横滚角
        heading: 航向角
    
    Returns:
        rectified_image: 矫正后的图像
    """
    return apply_spherical_rectification(image, pitch, roll)
