# -*- coding: utf-8 -*-
"""
线段检测模块
"""
import numpy as np
import cv2
from PIL import Image


def detect_lines_lsd(image, scale=0.8):
    """
    使用LSD算法检测线段
    
    Args:
        image: PIL Image或numpy数组
        scale: 缩放因子
    
    Returns:
        lines: 检测到的线段，格式为 [x1, y1, x2, y2]
    """
    if isinstance(image, Image.Image):
        gray = np.array(image.convert('L'))
    else:
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
    
    # 创建LSD检测器
    lsd = cv2.createLineSegmentDetector()
    
    # 检测线段
    lines = lsd.detect(gray)[0]
    
    if lines is not None:
        lines = lines.reshape(-1, 4)
    else:
        lines = np.array([]).reshape(0, 4)
    
    return lines


def filter_lines(lines, width, height, min_length_ratio=0.01):
    """
    过滤线段
    
    Args:
        lines: 线段数组
        width, height: 图像尺寸
        min_length_ratio: 最小长度比例
    
    Returns:
        filtered_lines: 过滤后的线段
    """
    if len(lines) == 0:
        return lines
    
    # 计算最小长度阈值
    min_length = np.sqrt(width * height) * min_length_ratio
    
    # 计算线段长度
    dx = lines[:, 2] - lines[:, 0]
    dy = lines[:, 3] - lines[:, 1]
    lengths = np.sqrt(dx*dx + dy*dy)
    
    # 过滤短线段
    valid_mask = lengths >= min_length
    filtered_lines = lines[valid_mask]
    
    return filtered_lines


def convert_lines_to_homogeneous(lines, width, height, focal):
    """
    将线段转换为齐次坐标
    
    Args:
        lines: 线段数组 [x1, y1, x2, y2]
        width, height: 图像尺寸
        focal: 焦距
    
    Returns:
        lines_homo: 齐次线坐标
    """
    if len(lines) == 0:
        return np.array([]).reshape(3, 0)
    
    lines_homo = []
    
    for line in lines:
        x1, y1, x2, y2 = line
        
        # 转换到标准化坐标
        x1_norm = (x1 - width / 2) / focal
        y1_norm = (y1 - height / 2) / focal
        x2_norm = (x2 - width / 2) / focal
        y2_norm = (y2 - height / 2) / focal
        
        # 计算齐次线
        p1 = np.array([x1_norm, y1_norm, 1])
        p2 = np.array([x2_norm, y2_norm, 1])
        line_homo = np.cross(p1, p2)
        
        # 标准化
        if np.linalg.norm(line_homo[:2]) > 1e-10:
            line_homo = line_homo / np.linalg.norm(line_homo[:2])
        
        lines_homo.append(line_homo)
    
    return np.array(lines_homo).T


def group_lines_by_angle(lines, angle_threshold=np.pi/12):
    """
    根据角度对线段进行分组
    
    Args:
        lines: 线段数组
        angle_threshold: 角度阈值
    
    Returns:
        groups: 线段分组列表
    """
    if len(lines) == 0:
        return []
    
    # 计算线段角度
    dx = lines[:, 2] - lines[:, 0]
    dy = lines[:, 3] - lines[:, 1]
    angles = np.arctan2(dy, dx)
    
    # 标准化角度到 [0, pi)
    angles = np.mod(angles, np.pi)
    
    groups = []
    used = np.zeros(len(lines), dtype=bool)
    
    for i in range(len(lines)):
        if used[i]:
            continue
        
        # 创建新组
        group = [i]
        used[i] = True
        
        # 查找相似角度的线段
        for j in range(i+1, len(lines)):
            if used[j]:
                continue
            
            angle_diff = abs(angles[i] - angles[j])
            angle_diff = min(angle_diff, np.pi - angle_diff)
            
            if angle_diff <= angle_threshold:
                group.append(j)
                used[j] = True
        
        groups.append(group)
    
    return groups
