# -*- coding: utf-8 -*-
"""
几何计算工具函数
"""
import numpy as np


def normalize_coordinates(lines, width, height, focal):
    """标准化坐标系"""
    lines_norm = lines.copy()
    lines_norm[:, 0] = (lines[:, 0] - width / 2) / focal
    lines_norm[:, 1] = (lines[:, 1] - height / 2) / focal
    lines_norm[:, 2] = (lines[:, 2] - width / 2) / focal
    lines_norm[:, 3] = (lines[:, 3] - height / 2) / focal
    return lines_norm


def line_hmg_from_two_points(p1, p2):
    """从两点计算齐次线"""
    p1_hmg = np.array([p1[0], p1[1], 1])
    p2_hmg = np.array([p2[0], p2[1], 1])
    line = np.cross(p1_hmg, p2_hmg)
    return line / np.linalg.norm(line[:2])


def line_hmg_intersect(line1, line2):
    """计算两条齐次线的交点"""
    point = np.cross(line1, line2)
    if abs(point[2]) > 1e-10:
        point = point / point[2]
    return point[:2]


def lines_normal(lines_homo):
    """计算线束的法向量（消失点）"""
    if lines_homo.shape[1] < 2:
        return np.array([0, 0, 1])
    
    # 使用SVD计算最小二乘解
    U, S, Vt = np.linalg.svd(lines_homo.T)
    vp = Vt[-1, :]
    
    # 标准化
    if abs(vp[2]) > 1e-10:
        vp = vp / vp[2]
    
    return vp


def line_angle(line):
    """计算线段的角度"""
    dx = line[2] - line[0]
    dy = line[3] - line[1]
    return np.arctan2(dy, dx)


def line_length(line):
    """计算线段长度"""
    dx = line[2] - line[0]
    dy = line[3] - line[1]
    return np.sqrt(dx*dx + dy*dy)


def rotation_matrix_x(angle):
    """绕X轴旋转矩阵"""
    c = np.cos(angle)
    s = np.sin(angle)
    return np.array([[1, 0, 0],
                     [0, c, -s],
                     [0, s, c]])


def rotation_matrix_y(angle):
    """绕Y轴旋转矩阵"""
    c = np.cos(angle)
    s = np.sin(angle)
    return np.array([[c, 0, s],
                     [0, 1, 0],
                     [-s, 0, c]])


def rotation_matrix_z(angle):
    """绕Z轴旋转矩阵"""
    c = np.cos(angle)
    s = np.sin(angle)
    return np.array([[c, -s, 0],
                     [s, c, 0],
                     [0, 0, 1]])


def R_heading(angle):
    """航向角旋转矩阵"""
    return rotation_matrix_z(angle)


def R_pitch(angle):
    """俯仰角旋转矩阵"""
    return rotation_matrix_x(angle)


def R_roll(angle):
    """横滚角旋转矩阵"""
    return rotation_matrix_y(angle)


def filter_lines_by_length(lines, min_length):
    """根据长度过滤线段"""
    lengths = np.array([line_length(line) for line in lines])
    valid_indices = lengths >= min_length
    return lines[valid_indices], valid_indices


def filter_lines_by_angle(lines, target_angle, angle_threshold):
    """根据角度过滤线段"""
    angles = np.array([line_angle(line) for line in lines])
    angle_diffs = np.abs(angles - target_angle)
    # 处理角度环绕
    angle_diffs = np.minimum(angle_diffs, 2*np.pi - angle_diffs)
    valid_indices = angle_diffs <= angle_threshold
    return lines[valid_indices], valid_indices
