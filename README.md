# 全景图矫正系统

基于Simon算法的全景图自动矫正工具，可以自动检测和矫正全景图中的几何失真。

## 功能特点

- **自动矫正**: 使用Simon算法自动检测消失点和地平线
- **批量处理**: 支持批量处理多张全景图
- **简单易用**: 只需将图片放入指定文件夹即可
- **多格式支持**: 支持 JPG、PNG、BMP、TIFF 等常见图像格式

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 批量处理模式

1. 将需要矫正的全景图放入 `picture/rawdata/` 文件夹
2. 运行主程序：
   ```bash
   python main.py
   ```
3. 矫正后的图片将保存在 `picture/rectdata/` 文件夹

### 2. 测试单张图像

```bash
python main.py test <图像路径>
```

例如：
```bash
python main.py test picture/rawdata/panorama.jpg
```

## 项目结构

```
.
├── main.py                     # 主程序
├── requirements.txt            # 依赖包列表
├── README.md                   # 使用说明
├── picture/                    # 图片文件夹
│   ├── rawdata/               # 原始图片（输入）
│   └── rectdata/              # 矫正后图片（输出）
└── panorama_rectification/     # 矫正算法包
    ├── __init__.py
    ├── config.py              # 参数配置
    ├── simon_algorithm.py     # Simon算法核心
    ├── line_detection.py      # 线段检测
    ├── vanishing_point.py     # 消失点检测
    ├── orthorectify.py        # 正交矫正
    └── geometry_utils.py      # 几何工具函数
```

## 算法原理

本系统基于Simon等人提出的全景图矫正算法，主要步骤包括：

1. **线段检测**: 使用LSD算法检测图像中的直线段
2. **消失点检测**: 通过RANSAC算法检测垂直和水平消失点
3. **天顶点估计**: 基于垂直线段估计天顶点位置
4. **角度计算**: 计算俯仰角和横滚角
5. **球面矫正**: 应用旋转变换矫正全景图

## 参数说明

主要参数在 `panorama_rectification/config.py` 中定义：

- `theta_z`: 天顶角度阈值 (默认: 10°)
- `theta_h`: 水平角度阈值 (默认: 1.5°)
- `ransac_iter_num`: RANSAC迭代次数 (默认: 50)

## 注意事项

1. **输入图像要求**: 
   - 建议使用高质量的全景图
   - 图像中应包含足够的直线结构（如建筑物边缘）

2. **处理时间**: 
   - 处理时间取决于图像尺寸和复杂度
   - 大尺寸图像可能需要较长时间

3. **矫正效果**: 
   - 算法对包含明显几何结构的图像效果更好
   - 对于缺乏直线特征的自然景观，可能使用默认处理

## 故障排除

### 常见问题

1. **找不到图像文件**
   - 检查 `picture/rawdata/` 文件夹是否存在
   - 确认图像格式是否支持

2. **处理失败**
   - 检查图像是否损坏
   - 确认依赖包是否正确安装

3. **矫正效果不佳**
   - 尝试调整参数配置
   - 确保输入图像包含足够的直线特征

### 错误信息

- `输入文件夹不存在`: 需要创建 `picture/rawdata/` 文件夹
- `未找到图像文件`: 检查文件格式和路径
- `处理失败`: 查看具体错误信息，可能是图像格式或算法问题

## 技术支持

如有问题或建议，请检查：
1. 依赖包是否正确安装
2. 输入图像是否符合要求
3. 参数配置是否合适

## 版本信息

- 版本: 1.0.0
- 基于: Simon et al. 全景图矫正算法
- 开发语言: Python 3.7+
