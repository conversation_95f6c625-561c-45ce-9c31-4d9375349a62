#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全景图矫正主程序

使用方法:
    python main.py

功能:
    - 从 picture/rawdata/ 文件夹读取原始全景图
    - 使用Simon算法进行矫正
    - 将矫正后的图片保存到 picture/rectdata/ 文件夹
"""

import os
import sys
import glob
import time
from PIL import Image
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from panorama_rectification import simon_rectification
from panorama_rectification import 

def main():
    """主函数"""
    print("=" * 60)
    print("全景图矫正系统")
    print("=" * 60)
    
    # 定义输入输出文件夹
    input_folder = "picture/rawdata"
    output_folder = "picture/rectdata"
    
    # 检查输入文件夹是否存在
    if not os.path.exists(input_folder):
        print(f"错误: 输入文件夹 '{input_folder}' 不存在")
        print("请创建该文件夹并放入需要矫正的全景图")
        return
    
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")
    
    # 查找图像文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
    
    if not image_files:
        print(f"在 '{input_folder}' 文件夹中未找到图像文件")
        print("支持的格式: jpg, jpeg, png, bmp, tiff")
        return
    
    print(f"找到 {len(image_files)} 张图像")
    print("-" * 60)
    
    # 处理每张图像
    success_count = 0
    total_time = 0
    
    for i, image_path in enumerate(image_files):
        filename = os.path.basename(image_path)
        print(f"[{i+1}/{len(image_files)}] 处理: {filename}")
        
        start_time = time.time()
        
        try:
            # 矫正图像
            rectified_image, pitch, roll, success = simon_rectification(image_path)
            
            # 保存结果
            output_path = os.path.join(output_folder, filename)
            
            if isinstance(rectified_image, np.ndarray):
                # 确保数据类型正确
                if rectified_image.dtype != np.uint8:
                    rectified_image = np.clip(rectified_image, 0, 255).astype(np.uint8)
                rectified_pil = Image.fromarray(rectified_image)
            else:
                rectified_pil = rectified_image
            
            rectified_pil.save(output_path, quality=95)
            
            process_time = time.time() - start_time
            total_time += process_time
            
            status = "算法矫正" if success else "默认处理"
            print(f"  ✓ {status} - 耗时: {process_time:.2f}s")
            print(f"    俯仰角: {pitch:.3f} rad ({pitch*180/np.pi:.1f}°)")
            print(f"    横滚角: {roll:.3f} rad ({roll*180/np.pi:.1f}°)")
            print(f"    保存至: {output_path}")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
            continue
        
        print()
    
    # 输出统计信息
    print("-" * 60)
    print("处理完成!")
    print(f"成功处理: {success_count}/{len(image_files)} 张图像")
    print(f"总耗时: {total_time:.2f}s")
    if success_count > 0:
        print(f"平均耗时: {total_time/success_count:.2f}s/张")
    print(f"结果保存在: {output_folder}")


def test_single_image(image_path):
    """测试单张图像的矫正功能"""
    print(f"测试图像: {image_path}")
    
    if not os.path.exists(image_path):
        print("图像文件不存在")
        return
    
    try:
        start_time = time.time()
        rectified_image, pitch, roll, success = simon_rectification(image_path)
        process_time = time.time() - start_time
        
        print(f"处理完成 - 耗时: {process_time:.2f}s")
        print(f"算法状态: {'成功' if success else '使用默认处理'}")
        print(f"俯仰角: {pitch:.3f} rad ({pitch*180/np.pi:.1f}°)")
        print(f"横滚角: {roll:.3f} rad ({roll*180/np.pi:.1f}°)")
        
        # 保存测试结果
        output_path = "test_rectified.jpg"
        if isinstance(rectified_image, np.ndarray):
            rectified_pil = Image.fromarray(rectified_image.astype(np.uint8))
        else:
            rectified_pil = rectified_image
        
        rectified_pil.save(output_path)
        print(f"测试结果保存至: {output_path}")
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "test" and len(sys.argv) > 2:
            # 测试模式
            test_single_image(sys.argv[2])
        else:
            print("使用方法:")
            print("  python main.py              # 批量处理")
            print("  python main.py test <图像路径>  # 测试单张图像")
    else:
        # 正常批量处理模式
        main()
