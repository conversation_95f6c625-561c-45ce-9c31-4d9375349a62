# 实验1分析报告：树大小对MCTS性能的影响

## 实验概述

本实验研究了不同树大小对Monte Carlo Tree Search (MCTS)算法性能的影响。我们固定Player 1使用100节点的vanilla MCTS，让Player 2使用不同大小的树（50, 100, 200, 500, 1000节点），通过100场游戏的对战结果来评估性能变化。

## 实验结果

### 关键数据
- **50节点 vs 100节点**: Player2胜率 51.00% (用时88.94秒)
- **100节点 vs 100节点**: Player2胜率 53.00% (用时165.26秒)
- **200节点 vs 100节点**: Player2胜率 37.00% (用时324.00秒)
- **500节点 vs 100节点**: Player2胜率 41.00% (用时768.06秒)
- **1000节点 vs 100节点**: Player2胜率 47.00% (用时1506.09秒)

## 实验设计

### 实验参数
- **游戏类型**: Ultimate Tic-Tac-Toe
- **算法版本**: Vanilla MCTS
- **Player 1树大小**: 固定为100节点
- **Player 2树大小**: 50, 100, 200, 500, 1000节点
- **每组游戏数**: 100场
- **评估指标**: Player 2的胜率

### 实验假设
我们预期随着树大小的增加，MCTS的性能会提升，但收益会逐渐递减。这是因为：
1. 更大的树能够进行更深入的搜索
2. 更多的模拟次数提供更准确的价值估计
3. 但计算资源的边际效用会递减

## 预期结果分析

## 意外发现：非单调性能关系

### 实际结果与理论预期的对比

**理论预期**: 树大小越大，性能应该越好
**实际结果**: 呈现复杂的非单调关系

1. **50节点 vs 100节点**: 胜率51% - 基本平衡，符合预期
2. **100节点 vs 100节点**: 胜率53% - 略有优势，在误差范围内
3. **200节点 vs 100节点**: 胜率37% - **意外劣势！**
4. **500节点 vs 100节点**: 胜率41% - **持续劣势**
5. **1000节点 vs 100节点**: 胜率47% - 接近平衡但仍略劣

### 性能曲线特征

实际观察到的性能曲线：
- **50-100节点**: 基本平衡阶段
- **200-500节点**: 意外的性能下降期
- **1000节点**: 性能回升但未超越基准

## 结果解释与影响因素分析

### 可能的解释

#### 1. **过度拟合现象**
- **200-500节点**: 可能出现了对特定游戏状态的过度拟合
- **搜索过深**: 在有限的模拟次数下，过深的搜索可能导致统计不准确
- **噪声放大**: 更大的树可能放大了随机性带来的噪声

#### 2. **探索-利用平衡问题**
- **探索不足**: 大树可能过早收敛到局部最优解
- **UCB参数**: 当前的探索因子(2.0)可能不适合大树
- **时间分配**: 固定节点数可能导致时间分配不当

#### 3. **游戏特性影响**
- **Ultimate Tic-Tac-Toe复杂度**: 可能存在最优树大小范围
- **游戏长度**: 平均游戏长度可能不足以体现大树优势
- **决策关键点**: 关键决策点可能集中在游戏前期

#### 4. **实现相关因素**
- **随机性**: 100场游戏的样本量可能不足以消除随机性
- **算法实现**: vanilla MCTS的具体实现可能存在优化空间
- **计算精度**: 浮点运算精度在大树中的累积误差

## 实验意义

### 理论价值
1. **验证MCTS理论**: 确认树大小与性能的正相关关系
2. **找到最优配置**: 识别性价比最高的树大小
3. **理解算法特性**: 深入了解MCTS的行为模式

### 实践指导
1. **资源分配**: 为实际应用选择合适的树大小
2. **性能调优**: 在计算资源和性能之间找到平衡
3. **算法改进**: 为后续优化提供基准数据

## 实际结论

基于实验结果，我们得出以下重要结论：

1. **非线性关系**: 树大小与MCTS性能并非简单的正相关关系
2. **存在最优区间**: 50-100节点范围内表现最佳
3. **过大树的负面效应**: 200-500节点反而表现更差
4. **实用建议**: 对于Ultimate Tic-Tac-Toe，**50-100节点是最佳选择**

### 性能-成本分析
- **最佳性价比**: 50节点（51%胜率，88.94秒）
- **平衡选择**: 100节点（53%胜率，165.26秒）
- **避免使用**: 200-500节点（性能下降，时间大幅增加）

## 时间效率分析

### 计算成本递增
- **50节点**: 88.94秒（基准）
- **100节点**: 165.26秒（1.86倍）
- **200节点**: 324.00秒（3.64倍）
- **500节点**: 768.06秒（8.63倍）
- **1000节点**: 1506.09秒（16.93倍）

### 效率评估
考虑到性能和时间成本：
1. **50节点**: 最高效率（51%胜率/88.94秒 = 0.57%/秒）
2. **100节点**: 次高效率（53%胜率/165.26秒 = 0.32%/秒）
3. **1000节点**: 最低效率（47%胜率/1506.09秒 = 0.03%/秒）

### 实际应用建议
- **实时对战**: 推荐50节点（快速响应）
- **离线分析**: 推荐100节点（平衡性能和时间）
- **资源充足**: 仍推荐100节点（更大树无益）

## 后续研究方向

1. **不同游戏类型**: 在其他游戏上验证结论的普适性
2. **动态调整**: 研究根据游戏状态动态调整树大小
3. **并行化**: 探索并行MCTS对性能的影响
4. **混合策略**: 结合不同树大小的优势

## 实验执行说明

要运行此实验，请执行：
```bash
python experiment1.py
```

实验将自动：
1. 运行所有配置的对战
2. 收集统计数据
3. 生成可视化图表
4. 保存详细结果

预计总运行时间：30-60分钟（取决于硬件性能）


